{"name": "ollama", "version": "0.5.16", "description": "Ollama Javascript library", "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"require": "./dist/index.cjs", "import": "./dist/index.mjs", "types": "./dist/index.d.ts"}, "./browser": {"require": "./dist/browser.cjs", "import": "./dist/browser.mjs", "types": "./dist/browser.d.ts"}, "./*": "./*"}, "scripts": {"format": "prettier --write .", "test": "vitest --run", "build": "unbuild", "lint": "eslint ./src/*", "prepublishOnly": "npm run build"}, "homepage": "https://github.com/ollama/ollama-js", "repository": {"type": "git", "url": "https://github.com/ollama/ollama-js.git"}, "author": "<PERSON>", "license": "MIT", "devDependencies": {"@swc/core": "^1.3.14", "@types/whatwg-fetch": "^0.0.33", "@typescript-eslint/eslint-plugin": "^5.42.1", "@typescript-eslint/parser": "^5.42.1", "eslint": "^8.29.0", "vitest": "^2.1.6", "prettier": "^3.2.4", "typescript": "^5.3.2", "unbuild": "^2.0.0"}, "dependencies": {"whatwg-fetch": "^3.6.20"}}