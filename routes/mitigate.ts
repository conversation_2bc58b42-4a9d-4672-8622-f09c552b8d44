import { Router } from "express";
import ollama from "ollama";
import { z } from "zod";
import * as fs from "fs";
import * as path from "path";

const mitigateRouter = Router();

// Load assets data
const loadAssets = () => {
  try {
    const secureComponentsPath = path.join(
      process.cwd(),
      "assets",
      "secure_network_components.json"
    );
    const vulnerabilitiesPath = path.join(
      process.cwd(),
      "assets",
      "vulnerabilities.json"
    );

    const secureComponents = JSON.parse(
      fs.readFileSync(secureComponentsPath, "utf-8")
    );
    const vulnerabilities = JSON.parse(
      fs.readFileSync(vulnerabilitiesPath, "utf-8")
    );

    return { secureComponents, vulnerabilities };
  } catch (error) {
    console.error("Error loading assets:", error);
    return { secureComponents: null, vulnerabilities: null };
  }
};

// Input validation schema
const inputSchema = z.object({
  comparator: z.string().optional(),
  validate: z.string().optional(),
  user_query: z.string().optional(),
});

const outputSchema = z.object({
  greeting: z
    .string()
    .describe("A warm, human-like welcome message for the user."),

  user_intent: z
    .string()
    .describe("A high-level summary of what the user wants or is asking."),

  summary: z
    .string()
    .describe("A concise overview of the AI's understanding of the situation."),

  recommendations: z
    .array(
      z.object({
        title: z
          .string()
          .describe("The headline or label of the recommendation."),
        description: z
          .string()
          .describe("A detailed explanation of the recommendation."),
        priority: z
          .enum(["high", "medium", "low"])
          .describe("The importance level of the recommendation."),
        action_type: z
          .enum(["code", "diagram", "explanation", "reference"])
          .describe("The type of supporting action the AI is taking."),
        action_payload: z
          .union([z.string(), z.record(z.any())])
          .describe(
            "The actual action content: could be code, text, link, or structured data."
          ),
      })
    )
    .describe("List of actionable suggestions tailored to the user’s context."),

  comparisons: z
    .array(
      z.object({
        item: z
          .string()
          .describe(
            "The primary entity being evaluated (e.g., user’s input or component)."
          ),
        matched_against: z
          .string()
          .describe(
            "What it’s being compared to (e.g., a best practice or secure component)."
          ),
        result: z
          .enum(["match", "partial", "mismatch"])
          .describe("How closely the item aligns with the reference."),
        notes: z
          .string()
          .describe("Additional context or insight about the comparison."),
      })
    )
    .describe("List of comparisons for validation, auditing, or explanation."),

  integration_guidance: z
    .object({
      target_stack: z
        .enum(["MERN", "AWS", "Other"])
        .describe("The tech stack where this should be applied."),
      entry_points: z
        .array(z.string())
        .describe("Key places in the stack to integrate the recommendations."),
      sample_code: z
        .string()
        .describe(
          "Example code snippet showing how to apply the recommendation."
        ),
      security_notes: z
        .array(z.string())
        .describe("Extra considerations from a security point of view."),
    })
    .describe(
      "Guidance on how to plug the AI’s recommendations into the user’s system."
    ),

  next_steps: z
    .array(z.string())
    .describe("Suggested follow-up actions for the user to take."),

  html: z
    .string()
    .describe(
      "HTML content to display to the user. use visual tools to make it more interactive."
    ),
});

// Helper function to create system prompt
const createSystemPrompt = (assets: any, userInput: any) => {
  return `You are a Vendor AI Expert specializing in security, compliance, and technology solutions. Your role is to help users understand, evaluate, and integrate vendor technologies.

IMPORTANT: You MUST respond with valid JSON that exactly matches this schema:
${JSON.stringify(
  {
    greeting: "string - A warm, human-like welcome message",
    user_intent: "string - High-level summary of what the user wants",
    summary: "string - Concise overview of your understanding",
    recommendations: [
      {
        title: "string - Recommendation headline",
        description: "string - Detailed explanation",
        priority: "high|medium|low",
        action_type: "code|diagram|explanation|reference",
        action_payload: "string or object - The actual content",
      },
    ],
    comparisons: [
      {
        item: "string - What's being evaluated",
        matched_against: "string - What it's compared to",
        result: "match|partial|mismatch",
        notes: "string - Additional context",
      },
    ],
    integration_guidance: {
      target_stack: "MERN|AWS|Other",
      entry_points: ["string array - Key integration points"],
      sample_code: "string - Example code snippet",
      security_notes: ["string array - Security considerations"],
    },
    next_steps: ["string array - Follow-up actions"],
    html: "string - Interactive HTML content",
  },
  null,
  2
)}

Available Security Components Database:
${JSON.stringify(assets.secureComponents, null, 2)}

Known Vulnerabilities Database:
${JSON.stringify(assets.vulnerabilities?.slice(0, 10), null, 2)}

User Input Context:
${JSON.stringify(userInput, null, 2)}

Respond ONLY with valid JSON matching the exact schema above. Do not include any text before or after the JSON.`;
};

mitigateRouter.post("/ai", async (req, res) => {
  try {
    // Validate input
    const validationResult = inputSchema.safeParse(req.body);
    if (!validationResult.success) {
      res.status(400).json({
        error: "Invalid input",
        details: validationResult.error.errors,
      });
      return;
    }

    const { comparator, validate, user_query } = validationResult.data;
    const assets = loadAssets();

    if (!assets.secureComponents || !assets.vulnerabilities) {
      res.status(500).json({
        error: "Failed to load security assets",
        message:
          "Unable to access security components or vulnerabilities database",
      });
      return;
    }

    // Create comprehensive system prompt
    const systemPrompt = createSystemPrompt(assets, {
      comparator,
      validate,
      user_query,
    });

    const userMessage =
      user_query ||
      "Hi there! 👋 I'm looking for help with vendor AI technologies. Can you help me understand, evaluate, and integrate secure, compliant tech solutions? I'm particularly interested in security frameworks, product capabilities, and technical integrations.";

    const response = await ollama.chat({
      model: "deepseek-r1",
      messages: [
        {
          role: "system",
          content: systemPrompt,
        },
        {
          role: "user",
          content: userMessage,
        },
      ],
      think: false,
      stream: false,
    });

    // Parse and validate the AI response
    let aiResponse;
    try {
      aiResponse = JSON.parse(response.message.content);
    } catch (parseError) {
      console.error("Failed to parse AI response as JSON:", parseError);
      res.status(500).json({
        error: "Invalid AI response format",
        message: "The AI did not return valid JSON",
        raw_response: response.message.content,
      });
      return;
    }

    // Validate against schema
    const schemaValidation = outputSchema.safeParse(aiResponse);
    if (!schemaValidation.success) {
      console.error(
        "AI response doesn't match schema:",
        schemaValidation.error
      );
      res.status(500).json({
        error: "AI response validation failed",
        details: schemaValidation.error.errors,
        raw_response: aiResponse,
      });
      return;
    }

    // Return validated response
    res.status(200).json(schemaValidation.data);
  } catch (error) {
    console.error("Error in /ai endpoint:", error);
    res.status(500).json({
      error: "Internal server error",
      message:
        error instanceof Error ? error.message : "Unknown error occurred",
    });
  }
});

export default mitigateRouter;
