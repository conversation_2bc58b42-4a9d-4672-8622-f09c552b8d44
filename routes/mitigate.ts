import { Router } from "express";
import ollama from "ollama";
import { z } from "zod";

const mitigateRouter = Router();

const outputSchema = z.object({
  greeting: z
    .string()
    .describe("A warm, human-like welcome message for the user."),

  user_intent: z
    .string()
    .describe("A high-level summary of what the user wants or is asking."),

  summary: z
    .string()
    .describe("A concise overview of the AI's understanding of the situation."),

  recommendations: z
    .array(
      z.object({
        title: z
          .string()
          .describe("The headline or label of the recommendation."),
        description: z
          .string()
          .describe("A detailed explanation of the recommendation."),
        priority: z
          .enum(["high", "medium", "low"])
          .describe("The importance level of the recommendation."),
        action_type: z
          .enum(["code", "diagram", "explanation", "reference"])
          .describe("The type of supporting action the AI is taking."),
        action_payload: z
          .union([z.string(), z.record(z.any())])
          .describe(
            "The actual action content: could be code, text, link, or structured data."
          ),
      })
    )
    .describe("List of actionable suggestions tailored to the user’s context."),

  comparisons: z
    .array(
      z.object({
        item: z
          .string()
          .describe(
            "The primary entity being evaluated (e.g., user’s input or component)."
          ),
        matched_against: z
          .string()
          .describe(
            "What it’s being compared to (e.g., a best practice or secure component)."
          ),
        result: z
          .enum(["match", "partial", "mismatch"])
          .describe("How closely the item aligns with the reference."),
        notes: z
          .string()
          .describe("Additional context or insight about the comparison."),
      })
    )
    .describe("List of comparisons for validation, auditing, or explanation."),

  integration_guidance: z
    .object({
      target_stack: z
        .enum(["MERN", "AWS", "Other"])
        .describe("The tech stack where this should be applied."),
      entry_points: z
        .array(z.string())
        .describe("Key places in the stack to integrate the recommendations."),
      sample_code: z
        .string()
        .describe(
          "Example code snippet showing how to apply the recommendation."
        ),
      security_notes: z
        .array(z.string())
        .describe("Extra considerations from a security point of view."),
    })
    .describe(
      "Guidance on how to plug the AI’s recommendations into the user’s system."
    ),

  next_steps: z
    .array(z.string())
    .describe("Suggested follow-up actions for the user to take."),

  html: z
    .string()
    .describe(
      "HTML content to display to the user. use visual tools to make it more interactive."
    ),
});

mitigateRouter.get("/ai", async (req, res) => {
  const {
    body: { comparator, validate },
  } = req;

  const response = await ollama.chat({
    model: "deepseek-r1",
    messages: [
      {
        role: "system",
        content: JSON.stringify({ comparator, validate }),
      },
      {
        role: "user",
        content:
          "Hi there! 👋 I'm your Vendor AI Expert — here to help you understand, evaluate, and get the most from secure, compliant tech solutions. What would you like to explore today? Help users understand, evaluate, and integrate vendor technologies (security, compliance, cloud, etc.) You can guide you through security frameworks, product capabilities, or help with technical integrations. Are you evaluating specific tools, or looking to harden your existing stack?",
      },
    ],
    think: false,
    stream: false,
  });

  res.send(response.message.content);
});

export default mitigateRouter;
